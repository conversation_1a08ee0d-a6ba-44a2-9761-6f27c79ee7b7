# rag/processing/ingestion/csrd_processor.py
import pandas as pd
from bs4 import BeautifulSoup, Tag
import re
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class CSRDProcessor:
    """
    Parses CSRD HTML by isolating Appendix content and using a refined stop
    condition to correctly handle sub-headings within AR sections.
    """

    def __init__(self, html_content: str):
        self.soup = BeautifulSoup(html_content, 'html.parser')
        self.dr_df = pd.DataFrame(columns=['id', 'code', 'name', 'content', 'metadata'])
        self.dr_counter = 1
        self.seen_codes = set()
        self.ar_df = pd.DataFrame(columns=['content', 'dr_code_fk', 'metadata'])

    def _normalize_code(self, code: str) -> str:
        """Replaces various dash characters with a standard hyphen and strips whitespace."""
        return code.replace('–', '-').replace('—', '-').strip()

    def process_document(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Runs the full parsing process for DRs and ARs."""
        self._process_main_body_for_drs()
        self._process_all_appendices()
        return self.dr_df, self.ar_df

    def _process_main_body_for_drs(self):
        """Processes ONLY the DRs in the main body before any appendices."""
        logger.info("Processing main body for Disclosure Requirements...")
        appendix_a_start = self.soup.find('p', class_='oj-doc-ti', text=re.compile(r'^\s*Appendix A\s*'))

        all_dr_titles = self.soup.find_all('p', class_='oj-ti-grseq-1')

        for title_element in all_dr_titles:
            # Check if this is a valid DR title before proceeding
            if not re.search(r'Disclosure Requirement\s+([A-Z0-9]+-\d+)', title_element.get_text()):
                continue

            # If we've hit the appendix section, stop.
            if appendix_a_start and title_element.sourceline >= appendix_a_start.sourceline:
                break

            match = re.match(r'Disclosure Requirement\s+([A-Z0-9]+-\d+)\s*[–-]\s*(.*)',
                             title_element.get_text(strip=True))
            if match:
                self._extract_section(title_element, match, is_ar_chunk=False)

    def _process_all_appendices(self):
        """Finds each appendix and processes it in isolation."""
        logger.info("Processing all appendices for Application Requirements...")
        appendix_starts = self.soup.find_all('p', class_='oj-doc-ti', text=re.compile(r'^\s*Appendix\s+[A-Z]\s*$'))

        for i, start_tag in enumerate(appendix_starts):
            end_tag = appendix_starts[i + 1] if (i + 1) < len(appendix_starts) else None

            appendix_html_content = self._extract_content_between(start_tag, end_tag)
            if appendix_html_content:
                self._process_single_appendix(appendix_html_content)

    def _extract_content_between(self, start_tag: Tag, end_tag: Optional[Tag]) -> str:
        """Extracts all sibling tags between a start and end tag as an HTML string."""
        content = []
        for sibling in start_tag.find_next_siblings():
            if end_tag and sibling == end_tag:
                break
            content.append(str(sibling))
        return "".join(content)

    def _process_single_appendix(self, appendix_html: str):
        """Processes the HTML of a single, isolated appendix for AR chunks."""
        appendix_soup = BeautifulSoup(appendix_html, 'html.parser')
        ar_section_titles = appendix_soup.find_all('p', class_='oj-ti-grseq-1')

        for title_element in ar_section_titles:
            dr_code_fk = self._extract_fk_from_appendix_title(title_element.get_text(strip=True))
            if dr_code_fk:
                normalized_fk = self._normalize_code(dr_code_fk)
                self._extract_section(title_element, normalized_fk, is_ar_chunk=True)

    def _extract_fk_from_appendix_title(self, title_text: str) -> Optional[str]:
        """Extracts a DR code from a title string."""
        patterns = [
            r'Disclosure Requirement\s+([A-Z0-9]+-\d+)',
            r'related to ESRS \d+ ([A-Z0-9]+-\d+)'
        ]
        for pattern in patterns:
            match = re.search(pattern, title_text)
            if match:
                return match.group(1)
        return None

    def _extract_section(self, title_element: Tag, match_or_fk, is_ar_chunk: bool):
        """A unified function to extract a content section with a refined stop condition."""
        if is_ar_chunk:
            code = match_or_fk
            name = ""
        else:
            raw_code, name = match_or_fk.groups()
            code = self._normalize_code(raw_code)

        clean_code = code

        if clean_code in self.seen_codes:
            return
        self.seen_codes.add(clean_code)

        content_lines = []
        for sibling in title_element.find_next_siblings():
            # --- THE REFINED STOP CONDITION ---
            # Stop ONLY when we hit the next official DR title.
            # This allows subheadings like "Calculation guidance" to be included.
            if self._is_new_dr_heading(sibling):
                break

            line = self._get_content_from_element(sibling)
            if line:
                content_lines.append(line)

        full_content_string = '\n'.join(content_lines).strip()
        if not full_content_string:
            logger.warning(
                f"Section for {'AR linked to' if is_ar_chunk else 'DR'} {clean_code} has no content. Skipping.")
            return

        if is_ar_chunk:
            logger.debug(f"Adding AR chunk for DR: {clean_code}")
            new_row = pd.DataFrame([{'content': full_content_string, 'dr_code_fk': clean_code, 'metadata': {}}])
            self.ar_df = pd.concat([self.ar_df, new_row], ignore_index=True)
        else:
            logger.debug(f"Adding DR: {clean_code}")
            new_row = pd.DataFrame([{'id': self.dr_counter, 'code': clean_code, 'name': name.strip(),
                                     'content': full_content_string, 'metadata': {}}])
            self.dr_df = pd.concat([self.dr_df, new_row], ignore_index=True)
            self.dr_counter += 1

    # --- REFINED HELPER METHOD FOR STOPPING ---
    def _is_new_dr_heading(self, element: Tag) -> bool:
        """
        Checks if an element is a heading that marks the beginning of a new
        Disclosure Requirement section, ignoring other types of sub-headings.
        """
        if not isinstance(element, Tag) or element.name != 'p' or 'oj-ti-grseq-1' not in element.get('class', []):
            return False

        # A heading is a "stop" signal only if it contains these specific phrases.
        text = element.get_text()
        return "Disclosure Requirement" in text or "related to ESRS" in text

    def _get_content_from_element(self, element: Tag) -> Optional[str]:
        """Extracts text content from a paragraph or table element."""
        if not isinstance(element, Tag):
            return None

        table = element.find('table')
        if table:
            all_text_parts = []
            for row in table.find_all('tr'):
                row_text = " ".join([cell.get_text(" ", strip=True) for cell in row.find_all('td')]).strip()
                if row_text:
                    all_text_parts.append(row_text)
            return "\n".join(all_text_parts)
        elif element.name == 'p':
            return element.get_text(" ", strip=True)

        return None