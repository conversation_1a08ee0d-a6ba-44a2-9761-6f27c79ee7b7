# scripts/generate_esrs_candidates.py

import os
import json
import asyncio
import logging
from typing import List, Dict, Optional


from finetuning.esrs_data_helper import get_focused_esrs_catalogue_for_prompt
from rag.processing.chunking.semantic_chunker import SemanticChunker
from rag.processing.parsers.pdf_parser import PDFParser

# Assuming your project structure allows these imports
# Adjust if needed
from rag.services.ai.chat_completion_service import chat_completion_service
 # <-- UPDATED IMPORT


# ... (keep the QA_GENERATION_PROMPT if you still want it)

ESRS_CLASSIFICATION_PROMPT = {
    "system": (
        "You are an expert ESG and CSRD compliance auditor. Your task is to analyze a passage from a sustainability report "
        "and identify the single, most relevant European Sustainability Reporting Standard (ESRS) datapoint that the passage addresses. "
        "You will be given a list of possible ESRS datapoints to choose from."
    ),
    "user_template": (
        "Analyze the following text passage. From the provided ESRS Datapoint Catalogue, select the one datapoint ID that the text passage "
        "is most directly and substantively reporting on.\n\n"
        "Rules:\n"
        "1.  Choose ONLY from the provided list of datapoint IDs.\n"
        "2.  Select the MOST specific and relevant datapoint. For example, if the text mentions Scope 1 emissions figures, choose 'ESRS E1-6', not a general strategy datapoint.\n"
        "3.  If the text passage is generic, high-level, or does not seem to directly address any of the given datapoints, output 'N/A'.\n"
        "4.  The output MUST be a valid JSON object containing a single key, 'datapoint_id', with the selected ID as the value. Example: \"datapoint_id\": \"ESRS E1-6\"\n"
        "5.  Do not output anything else besides the JSON object.\n\n"
        "--- ESRS Datapoint Catalogue ---\n"
        "{esrs_catalogue_str}\n"
        "--- End of Catalogue ---\n\n"
        "--- Text Passage to Analyze ---\n"
        "{text_chunk}\n"
        "--- End of Text ---"
    )
}

CHUNK_SOURCE_FILE = "/Users/<USER>/PycharmProjects/parabella_deepsearch_agent/finetuning_texts/text_chunks_for_classification.jsonl"

# --- Configuration ---
RAW_TEXT_DIR = "/Users/<USER>/PycharmProjects/parabella_deepsearch_agent/finetuning_texts"
OUTPUT_CANDIDATES_FILE = "/Users/<USER>/PycharmProjects/parabella_deepsearch_agent/finetuning_texts/esrs_classification_candidates.jsonl"
GENERATOR_MODEL_ID = "openai_gpt-4.1-mini"
PDF_SOURCE_DIR = "/Users/<USER>/PycharmProjects/parabella_deepsearch_agent/finetuning_texts/pdf"
# We can afford a higher concurrency limit because each request is much smaller
CONCURRENT_REQUEST_LIMIT = 5
# Number of candidates to retrieve from the DB and include in the prompt
TOP_K_CANDIDATES_PER_CHUNK = 15
# --- New Chunker Configuration ---
MAX_WORDS_PER_CHUNK = 250  # Let's use a slightly larger word count for semantic chunks
SENTENCE_OVERLAP_WORDS = 20  # A bit more overlap can be helpful

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- The classify_chunk function remains the same as the last version ---
# It's already designed to take a text chunk and process it.
async def classify_chunk(semaphore: asyncio.Semaphore, text_chunk: str, filename: str, chunk_idx: int) -> Optional[
    Dict]:
    # ... (No changes needed here, copy the implementation from the previous answer)
    async with semaphore:
        logging.info(f"Processing chunk {chunk_idx + 1} for {filename}...")
        focused_esrs_catalogue = await get_focused_esrs_catalogue_for_prompt(
            text_chunk=text_chunk,
            top_k=TOP_K_CANDIDATES_PER_CHUNK
        )
        if not focused_esrs_catalogue:
            logging.warning(f"Skipping chunk {chunk_idx + 1} for {filename} because no relevant candidates were found.")
            return None
        user_prompt = ESRS_CLASSIFICATION_PROMPT["user_template"].format(
            esrs_catalogue_str=focused_esrs_catalogue,
            text_chunk=text_chunk
        )
        messages = [
            {"role": "system", "content": ESRS_CLASSIFICATION_PROMPT["system"]},
            {"role": "user", "content": user_prompt}
        ]


        try:
            response = await chat_completion_service.chat_completion(
                messages=messages, model_identifier=GENERATOR_MODEL_ID, temperature=0.0,
                response_format={"type": "json_object"}
            )
            if not response or not response.choices: return None
            content = response.choices[0].message.content
            data = json.loads(content)
            datapoint_id = data.get("datapoint_id")
            if datapoint_id and datapoint_id != "N/A":
                return {"text_passage": text_chunk, "suggested_datapoint_id": datapoint_id, "source_file": filename}
            return None
        except Exception as e:
            logging.error(f"Failed to process chunk {chunk_idx + 1} for {filename}. Error: {e}")
            return None


async def main():
    if not os.path.exists(CHUNK_SOURCE_FILE):
        logging.critical(f"Chunk source file not found: {CHUNK_SOURCE_FILE}. Please run 'extract_and_chunk.py' first.")
        return

    if os.path.exists(OUTPUT_CANDIDATES_FILE):
        os.remove(OUTPUT_CANDIDATES_FILE)

    # Read all the pre-made chunks from the file
    with open(CHUNK_SOURCE_FILE, 'r', encoding='utf-8') as f:
        all_chunks = [json.loads(line) for line in f]

    semaphore = asyncio.Semaphore(CONCURRENT_REQUEST_LIMIT)
    logging.info(f"Starting classification for {len(all_chunks)} pre-made chunks...")

    # Create classification tasks for each chunk
    tasks = [
        classify_chunk(
            semaphore,
            chunk['text_passage'],
            chunk['source_file'],
            i
        )
        for i, chunk in enumerate(all_chunks)
    ]

    results = await asyncio.gather(*tasks)

    # Save the results
    with open(OUTPUT_CANDIDATES_FILE, 'a', encoding='utf-8') as f_out:
        successful_chunks = 0
        for result in results:
            if result:
                f_out.write(json.dumps(result) + '\n')
                successful_chunks += 1
        logging.info(f"Finished. Successfully classified {successful_chunks}/{len(all_chunks)} chunks.")


if __name__ == "__main__":
    asyncio.run(main())