# scripts/curate_esrs_classifications.py

import json
import os
import textwrap

# The import path should point to your new helper file
# Assuming it's in a 'finetuning' directory at the same level as 'scripts'
from finetuning.esrs_data_helper import get_esrs_catalogue_for_curation

# --- Configuration ---
INPUT_CANDIDATES_FILE = "/Users/<USER>/PycharmProjects/parabella_deepsearch_agent/finetuning_texts/esrs_classification_candidates.jsonl"
FINAL_DATASET_FILE = "/Users/<USER>/PycharmProjects/parabella_deepsearch_agent/finetuning_texts/esrs_training_dataset.jsonl"


def format_for_finetuning(passage: str, datapoint_id: str, datapoint_desc: str) -> str:
    """Creates the final instruction-formatted string for the training file."""
    instruction = "Given the following text passage, identify the corresponding ESRS datapoint ID and its description."
    # <<< NOTE THE KEY CHANGE HERE >>>
    # The response format now explicitly uses "Description" to match the prompt
    response = f"Datapoint ID: {datapoint_id}\nDescription: {datapoint_desc}"
    return f"<s>[INST] {instruction}\n\nPassage:\n{passage} [/INST] {response}</s>"


# ... (get_already_processed_passages function can remain the same) ...
def get_already_processed_passages() -> set:
    processed_passages = set()
    if not os.path.exists(FINAL_DATASET_FILE):
        return processed_passages
    with open(FINAL_DATASET_FILE, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line)
                inst_block = data['text'].split('[/INST]')[0]
                passage = inst_block.split('Passage:\n')[-1].strip()
                processed_passages.add(passage)
            except (json.JSONDecodeError, IndexError, KeyError):
                continue
    return processed_passages


def curate_classifications():
    esrs_catalogue = get_esrs_catalogue_for_curation()
    if not esrs_catalogue:
        print("Error: Could not fetch ESRS catalogue. Aborting curation.")
        return
    esrs_ids = list(esrs_catalogue.keys())

    # ... (loading candidates and filtering logic remains the same) ...
    with open(INPUT_CANDIDATES_FILE, 'r', encoding='utf-8') as f_in:
        candidates = [json.loads(line) for line in f_in]
    already_processed = get_already_processed_passages()
    candidates_to_review = [c for c in candidates if c.get('text_passage') not in already_processed]

    print(f"\nStarting curation for {len(candidates_to_review)} new candidate classifications...")

    for i, candidate in enumerate(candidates_to_review):
        print("\n" + "=" * 80)
        print(f"Reviewing Candidate {i + 1}/{len(candidates_to_review)} | Source: {candidate['source_file']}")
        print("-" * 80)

        passage = candidate['text_passage']
        suggested_id = candidate['suggested_datapoint_id']

        print(f"[TEXT PASSAGE]:\n{textwrap.fill(passage, width=90)}\n")
        print("-" * 80)

        # <<< CRITICAL FIX HERE >>>
        # We now access the 'data_point_name' key, which exists in the dictionary.
        suggestion_desc = esrs_catalogue.get(suggested_id, {}).get('data_point_name', 'Unknown ID')
        print(f"LLM Suggestion: {suggested_id} - {suggestion_desc}")
        print("-" * 80)

        action = input("Action (y=yes, n=no, c=correct, q=quit): ").lower()

        if action == 'q': break
        if action == 'n': print("--- Rejected ---"); continue

        final_datapoint_id = suggested_id
        if action == 'c':
            print("\n--- Correcting Classification ---")
            for idx, dp_id in enumerate(esrs_ids):
                # <<< CRITICAL FIX HERE as well >>>
                # Use 'data_point_name' for display during correction.
                description = esrs_catalogue.get(dp_id, {}).get('data_point_name', '!!! MISSING NAME !!!')
                print(f"  {idx}: {dp_id} - {description}")

            try:
                selection = int(input(f"\nEnter the number for the correct datapoint (0-{len(esrs_ids) - 1}): "))
                if 0 <= selection < len(esrs_ids):
                    final_datapoint_id = esrs_ids[selection]
                    print(f"Corrected to: {final_datapoint_id}")
                else:
                    print("Invalid selection. Rejecting this candidate.");
                    continue
            except (ValueError, IndexError):
                print("Invalid input. Rejecting this candidate.");
                continue

        # <<< And the FINAL FIX here >>>
        # Get the final description using the correct key.
        final_datapoint_desc = esrs_catalogue.get(final_datapoint_id, {}).get('data_point_name')
        if not final_datapoint_desc:
            print(f"Warning: Could not find description for ID {final_datapoint_id}. Skipping.")
            continue

        formatted_string = format_for_finetuning(passage, final_datapoint_id, final_datapoint_desc)
        json_record = {"text": formatted_string}

        with open(FINAL_DATASET_FILE, 'a', encoding='utf-8') as f_out:
            f_out.write(json.dumps(json_record) + '\n')
        print("--- Approved & Saved ---")

    print("\nCuration complete.")


if __name__ == "__main__":
    curate_classifications()